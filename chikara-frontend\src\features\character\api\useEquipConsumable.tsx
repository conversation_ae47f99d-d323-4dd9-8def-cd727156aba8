import { api } from "@/helpers/api";
import { handlePost } from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

// TODO: Consumable equipment functionality needs to be migrated to oRPC
// The current endpoint (EQUIPCONSUMABLE) is a legacy placeholder
// and needs proper oRPC implementation in the backend

type EquipConsumableParams = {
    userItemId: number;
    slot: string;
};

type EquipConsumableResponse = {
    message: string;
};

/**
 * Custom hook to equip a consumable to a slot
 */
export const useEquipConsumable = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation<EquipConsumableResponse, Error, EquipConsumableParams>({
        mutationFn: async (params: EquipConsumableParams) => {
            const data = await handlePost<EquipConsumableResponse>(api.user.equipConsumable, params);
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: api.user.getEquippedConsumables.key() });
            toast.success("Consumable equipped");

            if (onSuccessCallback) {
                onSuccessCallback();
            }
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to equip consumable");
        },
    });
};

export default useEquipConsumable;
