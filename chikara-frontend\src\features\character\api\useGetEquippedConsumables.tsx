import { api } from "@/helpers/api";
import { handleGet } from "@/helpers/axiosInstance";
import { InventoryItem } from "@/types/item";
import { type UseQueryOptions, useQuery } from "@tanstack/react-query";

// TODO: Consumable equipment functionality needs to be migrated to oRPC
// The current endpoint (EQUIPPEDCONSUMABLES) is a legacy placeholder
// and needs proper oRPC implementation in the backend

export type ConsumableItem = {
    slot: number;
    userItemId: number;
    item: InventoryItem;
    count: number;
};

type EquippedConsumablesResponse = ConsumableItem[];

/**
 * Custom hook to fetch user's equipped consumables
 */
export const useGetEquippedConsumables = (
    options: Partial<UseQueryOptions<EquippedConsumablesResponse, Error, EquippedConsumablesResponse>> = {}
) => {
    return useQuery<EquippedConsumablesResponse, Error, EquippedConsumablesResponse>({
        queryKey: api.user.getEquippedConsumables.key(),
        queryFn: async () => {
            const data = await handleGet<EquippedConsumablesResponse>(api.user.getEquippedConsumables);
            return data;
        },
        ...options,
    });
};

export default useGetEquippedConsumables;
