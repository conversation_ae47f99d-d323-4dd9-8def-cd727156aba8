import { api } from "@/helpers/api";
import { handlePost } from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

// TODO: Consumable equipment functionality needs to be migrated to oRPC
// The current endpoint (UNEQUIPCONSUMABLE) is a legacy placeholder
// and needs proper oRPC implementation in the backend

type UnequipConsumableResponse = {
    message: string;
};

/**
 * Custom hook to unequip a consumable from a slot
 */
export const useUnequipConsumable = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (slot: string) => {
            const data = await handlePost<UnequipConsumableResponse>(api.user.unequipConsumable, { slot });
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: api.user.getEquippedConsumables.key() });
            toast.success("Consumable unequipped");

            if (onSuccessCallback) {
                onSuccessCallback();
            }
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to unequip consumable");
        },
    });
};

export default useUnequipConsumable;
